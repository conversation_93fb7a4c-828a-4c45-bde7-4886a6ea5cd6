package dtos

import (
	"time"

	"github.com/google/uuid"
)

// CreateSessionReq represents a request to create a new WhatsApp session
type CreateSessionReq struct {
	UseProxy bool `json:"use_proxy"`
	// ProxyAddress is the proxy address to use (if UseProxy is true)
	ProxyAddress string `json:"proxy_address"`
	// DeviceInfo contains custom device information to use
	DeviceInfo string `json:"device_info"`
	// AutoReconnect indicates if the session should automatically reconnect
	AutoReconnect   bool   `json:"auto_reconnect"`
	E164PhoneNumber string `json:"e_164_phone_number" validate:"required"` // Phone number in E.164 format
	IP              string `json:"ip"`
	Timezone        string `json:"timezone"`
}

// SessionResponse represents a session response
type SessionResponse struct {
	// ID is the unique identifier for the session
	ID uuid.UUID `json:"id"`
	// RegID is the registration ID of the WhatsApp device
	RegID string `json:"reg_id"`
	// JID is the WhatsApp JID (Jabber ID)
	JID string `json:"jid"`
	// Status represents the current state of the session
	Status string `json:"status"`
	// LastConnected is the timestamp of the last successful connection
	LastConnected time.Time `json:"last_connected"`
	// LastDisconnected is the timestamp of the last disconnection
	LastDisconnected time.Time `json:"last_disconnected"`
	// ConnectionAttempts tracks how many times we've tried to connect
	ConnectionAttempts int `json:"connection_attempts"`
	// ErrorMessage stores the last error message if any
	ErrorMessage string `json:"error_message"`
	// ProxyUsed indicates if a proxy was used for this session
	ProxyUsed bool `json:"proxy_used"`
	// AutoReconnect indicates if the session should automatically reconnect
	AutoReconnect bool `json:"auto_reconnect"`
	// MessageCount tracks the number of messages sent in this session
	MessageCount int `json:"message_count"`
	// CreatedAt is when the session was created
	CreatedAt time.Time `json:"created_at"`
	// Timezone is the session's timezone
	Timezone string `json:"timezone"`
	IP       string `json:"ip"`
}

// UpdateSessionReq represents a request to update a session
type UpdateSessionReq struct {
	// ID is the unique identifier for the session
	ID uuid.UUID `json:"id" validate:"required"`
	// Status represents the desired state of the session
	Status string `json:"status"`
	// AutoReconnect indicates if the session should automatically reconnect
	AutoReconnect *bool `json:"auto_reconnect"`
	// ProxyAddress is the proxy address to use
	ProxyAddress string `json:"proxy_address"`
}

// SessionSubscriptionReq represents a request to subscribe to presence updates
type SessionSubscriptionReq struct {
	// SessionID links to the session
	SessionID uuid.UUID `json:"session_id"`
	// Phone is the phone number to subscribe to
	Phone string `json:"phone" validate:"required"`
}

// SessionEventResponse represents an event that occurred during a session
type SessionEventResponse struct {
	// ID is the unique identifier for the event
	ID uuid.UUID `json:"id"`
	// SessionID links to the session
	SessionID uuid.UUID `json:"session_id"`
	// EventType describes what kind of event occurred
	EventType string `json:"event_type"`
	// Description provides details about the event
	Description string `json:"description"`
	// Timestamp records when the event occurred
	Timestamp time.Time `json:"timestamp"`
}

// CurrentPresenceResponse represents the current presence status for all subscribed phones
type CurrentPresenceResponse struct {
	// SessionID is the session identifier
	SessionID uuid.UUID `json:"session_id"`
	// Presences contains the latest presence status for each subscribed phone
	Presences []CurrentPresenceItem `json:"presences"`
	// UpdatedAt is when this data was last updated
	UpdatedAt time.Time `json:"updated_at"`
}

// CurrentPresenceItem represents the current presence status for a single phone
type CurrentPresenceItem struct {
	// Phone is the phone number
	Phone string `json:"phone"`
	// Status is the current presence status (online/offline)
	Status string `json:"status"`
	// LastSeen is when the user was last seen (for offline status)
	LastSeen *time.Time `json:"last_seen,omitempty"`
	// UpdatedAt is when this presence was last updated
	UpdatedAt time.Time `json:"updated_at"`
}

// PresenceHistoryReq represents a request for historical presence data
type PresenceHistoryReq struct {
	TimeRange string `json:"time_range,omitempty" validate:"omitempty,oneof=3h 24h 2d 3d"`
	StartDate string `json:"start_date,omitempty"`
	EndDate   string `json:"end_date,omitempty"`
	// Phone is optional filter for specific phone number
	Phone string `json:"phone,omitempty"`
	// Page is the page number for pagination
	Page int `json:"page"`
	// PerPage is the number of items per page
	PerPage int `json:"per_page"`
}

// PresenceHistoryResponse represents historical presence data
type PresenceHistoryResponse struct {
	// SessionID is the session identifier
	SessionID uuid.UUID `json:"session_id"`
	// TimeRange is the requested time range
	TimeRange string `json:"time_range"`
	// FromTime is the start of the time range
	FromTime time.Time `json:"from_time"`
	// ToTime is the end of the time range
	ToTime time.Time `json:"to_time"`
	// Presences contains the historical presence data
	Presences []PresenceHistoryItemByPhone `json:"presences"`
	// Total is the total number of presence records
	Total int64 `json:"total"`
	// Page is the current page number
	Page int `json:"page"`
	// PerPage is the number of items per page
	PerPage int `json:"per_page"`
	// TotalPages is the total number of pages
	TotalPages int `json:"total_pages"`
	// TotalOnlineTime is the total online time in seconds for all phones in the time range (independent of pagination)
}

// PresenceHistoryItem represents a single historical presence record

type PresenceHistoryItemByPhone struct {
	Phone           string                 `json:"phone"`
	Presences       []PresenceHistoryItem  `json:"-"`
	TotalOnlineTime float64                `json:"total_online_time"`
	DailySummaries  []DailyPresenceSummary `json:"daily_summaries"`
}

// DailyPresenceSummary represents daily presence summary for a phone
type DailyPresenceSummary struct {
	Date                string                `json:"date"`                   // YYYY-MM-DD format
	TotalOnlineDuration float64               `json:"total_online_duration"`  // Total online time in seconds for this day
	SessionCount        int                   `json:"session_count"`          // Number of online sessions this day
	FirstOnline         *time.Time            `json:"first_online,omitempty"` // First online time of the day
	LastOffline         *time.Time            `json:"last_offline,omitempty"` // Last offline time of the day
	Presences           []PresenceHistoryItem `json:"presences"`              // All presence records for this day
}
type PresenceHistoryItem struct {
	ID             uuid.UUID  `json:"-"`
	Phone          string     `json:"phone"`
	Status         string     `json:"status"`
	LastSeen       *time.Time `json:"last_seen,omitempty"`
	Timestamp      time.Time  `json:"timestamp"`
	OnlineDuration float64    `json:"online_duration,omitempty"`
	StartTime      time.Time  `json:"start_time,omitempty"`
	EndTime        time.Time  `json:"end_time,omitempty"`
}

// PresenceWithDuration represents a presence record with calculated online duration
type PresenceWithDuration struct {
	// ID is the unique identifier
	ID uuid.UUID `json:"id"`
	// CreatedAt is when the presence was created
	CreatedAt time.Time `json:"created_at"`
	// UpdatedAt is when the presence was last updated
	UpdatedAt time.Time `json:"updated_at"`
	// DeletedAt is when the presence was deleted (soft delete)
	DeletedAt *time.Time `json:"deleted_at"`
	// RegId is the registration ID
	RegId string `json:"reg_id"`
	// Status is the presence status (online/offline)
	Status string `json:"status"`
	// LastSeen is when the user was last seen
	LastSeen time.Time `json:"last_seen"`
	// SubscribePhone is the phone number being monitored
	SubscribePhone string `json:"subscribe_phone"`
	// OnlineDuration is how long the user was online (in seconds) - only for online status
	OnlineDuration *int64 `json:"online_duration,omitempty"`
}
